# 产品需求文档 (PRD) - STM32嵌入式控制系统

## 1. 文档信息
- **项目名称**: STM32F407嵌入式多功能控制系统
- **版本**: v1.0
- **创建日期**: 2025-01-29
- **负责人**: Emma (产品经理)
- **最后更新**: 2025-01-29

## 2. 背景与问题陈述

### 2.1 项目背景
本项目是一个基于STM32F407微控制器的嵌入式控制系统，主要用于工业自动化或机器人控制场景。系统集成了多种传感器、执行器和通信模块，实现了完整的感知-决策-执行闭环控制。

### 2.2 核心问题
- **多设备协调控制**: 需要同时管理多个UART通信、步进电机、传感器等设备
- **实时性要求**: 系统需要在严格的时间约束下响应外部事件
- **数据处理效率**: 需要高效处理来自多个传感器的数据流
- **系统稳定性**: 在长时间运行中保持系统稳定和可靠

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **O1**: 构建稳定可靠的多设备协调控制系统
2. **O2**: 实现高效的实时数据处理和响应机制
3. **O3**: 提供完整的系统监控和调试能力
4. **O4**: 确保系统的可扩展性和可维护性

### 3.2 关键结果 (Key Results)
- **KR1**: 系统响应时间 < 50ms (UART通信、电机控制)
- **KR2**: 系统连续运行时间 > 24小时无故障
- **KR3**: 支持至少4路UART并发通信
- **KR4**: 传感器数据采集频率 > 10Hz
- **KR5**: 代码覆盖率 > 80%

### 3.3 反向指标 (Counter Metrics)
- 系统内存使用率不超过80%
- CPU占用率不超过70%
- 通信错误率 < 1%

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 嵌入式系统开发工程师
- **次要用户**: 系统集成工程师、测试工程师
- **使用场景**: 工业自动化、机器人控制、实验室设备

### 4.2 用户故事
1. **作为开发工程师**，我希望能够快速集成新的传感器模块，以便扩展系统功能
2. **作为系统集成工程师**，我希望能够通过UART接口监控系统状态，以便进行远程调试
3. **作为测试工程师**，我希望能够通过标准化接口测试各个功能模块，以便验证系统可靠性

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 通信管理模块 (UART_APP)
- **功能描述**: 管理4路UART通信(UART2/4/5/6)，支持DMA传输
- **关键特性**:
  - 基于环形缓冲区的数据管理
  - 支持不同设备的数据处理回调
  - 自动错误检测和恢复
- **接口规范**:
  - `Uart_Init()`: 初始化所有UART实例
  - `Uart_Task()`: 周期性数据处理任务
  - 设备特定处理函数: `huart2_emm_handler()`, `huart4_emm_handler()`, `huart5_general_handler()`

#### 5.1.2 步进电机控制模块 (STEP_MOTOR_APP)
- **功能描述**: 控制X/Y轴双步进电机，支持速度和位置控制
- **关键特性**:
  - 速度控制: 支持-65535到+65535 RPM范围
  - 位置控制: 支持绝对和相对位置控制
  - 方向控制: 支持正反转控制
- **接口规范**:
  - `Step_Motor_Set_Speed(float x_rpm, float y_rpm)`: 设置双轴速度
  - `Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance)`: 位置控制
  - `Step_Motor_Stop()`: 紧急停止

#### 5.1.3 显示管理模块 (OLED_APP)
- **功能描述**: 管理OLED显示屏，提供系统状态显示
- **关键特性**:
  - 实时时间显示
  - 系统状态信息显示
  - 可扩展的显示内容管理
- **接口规范**:
  - `Oled_Init()`: 初始化显示屏
  - `Oled_Task()`: 周期性显示更新

#### 5.1.4 传感器管理模块
- **AHT20温湿度传感器** (AHT_APP):
  - 温度测量范围: -40°C到+85°C
  - 湿度测量范围: 0%到100%RH
  - 测量精度: ±0.3°C, ±2%RH
- **灰度传感器** (GRAY_APP):
  - 支持多路灰度检测
  - 可配置阈值设置
- **按键管理** (KEY_APP):
  - 支持多按键检测
  - 防抖处理
  - 长按/短按识别

#### 5.1.5 任务调度系统 (TASK)
- **功能描述**: 基于MultiTimer的任务调度系统
- **关键特性**:
  - 非阻塞式任务调度
  - 可配置任务周期
  - 任务优先级管理
- **任务配置**:
  - UART任务: 10ms周期
  - OLED任务: 100ms周期
  - 按键任务: 15ms周期
  - 灰度传感器: 20ms周期
  - 电机控制: 30ms周期
  - AHT20传感器: 200ms周期

### 5.2 系统架构特性

#### 5.2.1 硬件抽象层
- **HAL库集成**: 基于STM32 HAL库的标准化硬件接口
- **模块化设计**: Driver层和App层分离，便于移植和测试
- **配置管理**: 统一的配置文件管理(MyDefine.h)

#### 5.2.2 中间件集成
- **MultiTimer**: 软件定时器管理
- **RingBuffer**: 高效的环形缓冲区实现
- **PID控制器**: 闭环控制算法支持
- **Flash存储**: GD25QXX SPI Flash支持

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- ✅ 多路UART通信管理
- ✅ 双轴步进电机控制
- ✅ OLED显示管理
- ✅ 多种传感器集成(AHT20, 灰度, 按键)
- ✅ 实时任务调度系统
- ✅ Flash存储管理
- ✅ 系统初始化和配置管理

### 6.2 排除功能 (Out of Scope)
- ❌ 网络通信功能(WiFi/Ethernet)
- ❌ 图形用户界面(GUI)
- ❌ 文件系统支持
- ❌ 音频处理功能
- ❌ 复杂的图像处理算法

## 7. 依赖与风险

### 7.1 内部依赖
- STM32 HAL库版本兼容性
- 硬件时钟配置(168MHz系统时钟)
- DMA通道分配和优先级设置
- 中断优先级配置

### 7.2 外部依赖
- Keil MDK-ARM开发环境
- STM32CubeMX配置工具
- 硬件调试器(ST-Link)

### 7.3 潜在风险
- **高风险**: DMA冲突导致数据丢失
- **中风险**: 任务调度时序问题
- **低风险**: 传感器通信异常

### 7.4 风险缓解策略
- 实施严格的DMA通道管理和优先级设置
- 增加任务执行时间监控和异常处理
- 实现传感器通信重试机制和错误恢复

## 8. 发布初步计划

### 8.1 开发阶段
- **Phase 1**: 核心通信和电机控制模块 (2周)
- **Phase 2**: 传感器集成和显示功能 (1周)
- **Phase 3**: 系统集成测试和优化 (1周)

### 8.2 测试策略
- **单元测试**: 各功能模块独立测试
- **集成测试**: 模块间协作测试
- **压力测试**: 长时间运行稳定性测试
- **性能测试**: 响应时间和资源占用测试

### 8.3 部署计划
- **开发环境**: Keil MDK-ARM + STM32CubeMX
- **测试环境**: 硬件在环测试平台
- **生产环境**: 目标硬件平台部署

---

**文档状态**: ✅ 已完成
**下一步**: 技术架构设计和开发任务分解

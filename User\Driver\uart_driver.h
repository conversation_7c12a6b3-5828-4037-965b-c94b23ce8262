#ifndef __UART_DRIVER_H__
#define __UART_DRIVER_H__

#include "MyDefine.h"

#define BUFFER_SIZE 254

#define MAX_UARTS 4



typedef struct
{
    UART_HandleTypeDef *huart;              // UART句柄
    DMA_HandleTypeDef *hdma_rx;             // DMA接收句柄
    uint8_t rx_dma_buffer[BUFFER_SIZE];     // DMA接收缓冲区
    uint8_t ring_buffer_data[BUFFER_SIZE];  // ring buffer实际存储数据用的缓冲区
    struct rt_ringbuffer ring_buffer;       // ring buffer句柄
    uint8_t processing_buffer[BUFFER_SIZE]; // 从ring buffer取出数据进行处理的临时缓冲区
    uint8_t uart_id;                        // UART的标识符，如1表示USART1

    void (*process_data_handler)(uint8_t *data, uint16_t len); // 数据处理函数
} UartInstance_t;

extern UartInstance_t g_uart_instances[MAX_UARTS];

int Uart_Printf(UART_HandleTypeDef *huart, const char *format, ...);
int my_printf(const char *format, ...);
void HAL_UARTEx_RxeventCallback(UART_HandleTypeDef *huart, uint16_t Size);

#endif /* __UART_DRIVER_H__ */

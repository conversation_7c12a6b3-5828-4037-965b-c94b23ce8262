#ifndef __MYDEFINE_H
#define __MYDEFINE_H

#include "stdlib.h"
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include <stdint.h>
#include <stdbool.h>

#include "main.h"
#include "gpio.h"
#include "usart.h"
#include "i2c.h"
#include "tim.h"

#include "ringbuffer.h"
#include "MultiTimer.h"
#include "pid.h"
#include "ebtn.h"

#include "gd25qxx.h"
#include "motor_driver.h"
#include "motor_app.h"
#include "uart_driver.h"
#include "key_driver.h"
#include "hardware_iic.h"
#include "gray_app.h"
#include "key_app.h"
#include "uart_app.h"
#include "task.h"

#include "oled.h"
#include "oled_driver.h"
#include "oled_app.h"
#include "Emm_driver.h"
#include "step_motor_app.h"
#include "driver_aht20.h"
#include "aht_app.h"

#define UART_TASK_TIME 10
#define OLED_TASK_TIME 100
#define KEY_TASK_TIME 15
#define GRAY_TASK_TIME 20
#define MOTOR_TASK_TIME 30
#define AHT20_TASK_TIME 200

#endif // __mydefine_h__

--cpu=Cortex-M4.fp.sp
"2023.e\startup_stm32f407xx.o"
"2023.e\main.o"
"2023.e\gpio.o"
"2023.e\dma.o"
"2023.e\i2c.o"
"2023.e\spi.o"
"2023.e\tim.o"
"2023.e\usart.o"
"2023.e\stm32f4xx_it.o"
"2023.e\stm32f4xx_hal_msp.o"
"2023.e\stm32f4xx_hal_i2c.o"
"2023.e\stm32f4xx_hal_i2c_ex.o"
"2023.e\stm32f4xx_hal_rcc.o"
"2023.e\stm32f4xx_hal_rcc_ex.o"
"2023.e\stm32f4xx_hal_flash.o"
"2023.e\stm32f4xx_hal_flash_ex.o"
"2023.e\stm32f4xx_hal_flash_ramfunc.o"
"2023.e\stm32f4xx_hal_gpio.o"
"2023.e\stm32f4xx_hal_dma_ex.o"
"2023.e\stm32f4xx_hal_dma.o"
"2023.e\stm32f4xx_hal_pwr.o"
"2023.e\stm32f4xx_hal_pwr_ex.o"
"2023.e\stm32f4xx_hal_cortex.o"
"2023.e\stm32f4xx_hal.o"
"2023.e\stm32f4xx_hal_exti.o"
"2023.e\stm32f4xx_hal_spi.o"
"2023.e\stm32f4xx_hal_tim.o"
"2023.e\stm32f4xx_hal_tim_ex.o"
"2023.e\stm32f4xx_hal_uart.o"
"2023.e\system_stm32f4xx.o"
"2023.e\ringbuffer.o"
"2023.e\gd25qxx.o"
"2023.e\ebtn.o"
"2023.e\oled.o"
"2023.e\multitimer.o"
"2023.e\hardware_iic.o"
"2023.e\stm32f103_driver_aht20_interface.o"
"2023.e\driver_aht20.o"
"2023.e\oled_driver.o"
"2023.e\uart_driver.o"
"2023.e\emm_driver.o"
"2023.e\key_driver.o"
"2023.e\motor_driver.o"
"2023.e\oled_app.o"
"2023.e\uart_app.o"
"2023.e\step_motor_app.o"
"2023.e\key_app.o"
"2023.e\gray_app.o"
"2023.e\motor_app.o"
"2023.e\aht_app.o"
"2023.e\task.o"
--library_type=microlib --strict --scatter "2023.E\2023.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "2023.map" -o 2023.E\2023.E